// DeviceTestContent.jsx
import React from "react";
import Loader from "../../../components/Loader";
import DeviceTestingSection from "../components/DeviceTestingSection";
import TermsAndConditionsSection from "../components/TermsAndConditionsSection";
import InterviewSection from "../components/InterviewSection/InterviewSection";
import EndInterviewSection from "../components/EndInterviewSection";
import ReviewVideoProfile from "../components/ReviewVideoProfile";
import InterviewFlowHeader from "../components/InterviewFlowHeader";

export default function DeviceTestContent({
  isLoading,
  loadingText,
  currentSection,
  localCamStream,
  startWebcam,
  handleStartInterview,
  handleAcceptTerms,
  questions,
  currentQuestion,
  currentIndex,
  error,
  questionsLoading,
  updateAnswer,
  isInterviewActive,
  nextQuestion,
  handleEndInterview,
  startAnswering,
  status,
  finishVideoProfile,
  addPollyAudioToMix,
  videoUrl,
  score,
  termsAcceptedAndDismissed,
  setTermsAcceptedAndDismissed,
  onBack
}) {
  // Ensure the Start Interview button sets termsAcceptedAndDismissed to true
  const handleStartInterviewAndAcceptTerms = () => {
    if (setTermsAcceptedAndDismissed) setTermsAcceptedAndDismissed(true);
    if (handleStartInterview) handleStartInterview();
  };

  // Helper function to determine current section title
  const getCurrentSectionTitle = () => {
    switch (currentSection) {
      case "termsAndConditions":
        return "Terms & Conditions";
      case "deviceTesting":
        return "Device Setup & Testing";
      case "interview":
        return "Interview in Progress";
      case "reviewVideoProfile":
        return "Interview Results";
      case "endInterview":
        return "Interview Complete";
      case "loading":
        return "Processing";
      default:
        return "Video Interview";
    }
  };

  // Helper function to determine if header should be shown
  const shouldShowHeader = () => {
    // Show header for all sections including interview
    return true;
  };

  return (
    <div className="min-w-screen h-[100vh] bg-[#F4F7FE] text-gray-900 flex flex-col">
      {/* Header - Show for all sections */}
      {shouldShowHeader() && (
        <InterviewFlowHeader
          title={getCurrentSectionTitle()}
          showBackButton={currentSection !== "loading"}
          onBack={onBack}
          isInterviewSection={currentSection === "interview"}
          currentIndex={currentSection === "interview" ? currentIndex : null}
          totalQuestions={currentSection === "interview" && questions ? questions.length : null}
          showAudioStatus={currentSection === "interview"}
          showVideoStatus={currentSection === "interview"}
          progressPercentage={
            currentSection === "interview" && questions && questions.length > 0
              ? ((currentIndex + 1) / questions.length) * 100
              : null
          }
        />
      )}
      
      {/* Main Content */}
      <div className="flex-1 flex justify-center">
        {(isLoading || currentSection === "loading") && <Loader text={loadingText} />}

        {/* Show Terms & Conditions only if not dismissed */}
        {currentSection === "termsAndConditions" && !termsAcceptedAndDismissed && (
          <TermsAndConditionsSection onAccept={handleAcceptTerms} />
        )}

        {/* If terms are dismissed, proceed to deviceTesting or interview as appropriate */}
        {(currentSection === "termsAndConditions" && termsAcceptedAndDismissed) && (
          <div className="flex items-center w-full">
            <DeviceTestingSection
              localCamStream={localCamStream}
              startWebcam={startWebcam}
              onStartInterview={handleStartInterviewAndAcceptTerms}
            />
          </div>
        )}

        {currentSection === "deviceTesting" && (
          <div className="flex items-center w-full">
            <DeviceTestingSection
              localCamStream={localCamStream}
              startWebcam={startWebcam}
              onStartInterview={handleStartInterviewAndAcceptTerms}
            />
          </div>
        )}

        {currentSection === "interview" && (
          <>
            {(!questions || questions.length === 0) ? (
              <div className="flex h-screen items-center justify-center">
                <div className="max-w-md rounded-lg bg-blue-50 p-6 text-center">
                  <div className="mb-4">
                    <div className="mx-auto h-12 w-12 animate-spin rounded-full border-4 border-blue-200 border-t-blue-600"></div>
                  </div>
                  <h2 className="mb-4 text-xl font-semibold text-blue-700">
                    Loading Questions
                  </h2>
                </div>
              </div>
            ) : (
              <InterviewSection
                localCamStream={localCamStream}
                questions={questions}
                currentQuestion={currentQuestion}
                currentIndex={currentIndex}
                error={error}
                isLoading={questionsLoading}
                updateAnswer={updateAnswer}
                isInterviewActive={isInterviewActive}
                onNextQuestion={nextQuestion}
                onEndInterview={handleEndInterview}
                startAnswering={startAnswering}
                addPollyAudioToMix={addPollyAudioToMix}
              />
            )}
          </>
        )}
        {currentSection === "reviewVideoProfile" && (
          <ReviewVideoProfile
            status={status}
            finishVideoProfile={finishVideoProfile}
            videoUrl={videoUrl}
            score={score}
          />
        )}
        {currentSection === "endInterview" && <EndInterviewSection />}
      </div>
    </div>
  );
}