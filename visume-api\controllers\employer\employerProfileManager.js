// Employer Profile Management Functions

const pool = require("../../config/db");

// Fetch profiles by employer ID
exports.getProfilesByEmployerId = async (req, res) => {
  const { emp_id } = req.params;

  try {
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res.status(500).send("Failed to connect to the database.");
      }

      const sqlQuery = `
                SELECT *
                FROM employerprofiles
                WHERE emp_id = ?;
            `;

      connection.query(sqlQuery, [emp_id], (err, results) => {
        connection.release();

        if (err) {
          console.error("Error fetching profiles from database:", err);
          return res.status(500).send("Failed to fetch profiles.");
        }

        if (results.length === 0) {
          return res
            .status(404)
            .json({ message: "No profiles found for this employer." });
        }

        res.status(200).json({
          message: "Profiles fetched successfully.",
          data: results,
        });
      });
    });
  } catch (error) {
    console.error("Error fetching profiles:", error);
    res.status(500).send("Failed to fetch profiles.");
  }
};

// Get employer profile plan and candidate counts
exports.getEmployerProfilesData = async (req, res) => {
  const emp_id = req.headers["authorization"];

  if (!emp_id) {
    return res
      .status(400)
      .json({ message: "Unauthorized Access, Please Log in Again." });
  }

  try {
    const conn = await pool.promise().getConnection();

    try {
      const planIdQuery = `SELECT plan_id,creditsLeft,end_date FROM employerplans WHERE emp_id = ?;`;
      const [planIdResult] = await conn.query(planIdQuery, [emp_id]);

      if (!planIdResult.length) {
        return res.status(404).json({
          message: "No employer profile plan found. Please log in again.",
        });
      }

      const planId = planIdResult[0].plan_id;

      const planDetailsQuery = `SELECT * FROM plans WHERE id = ?;`;
      const [planDetails] = await conn.query(planDetailsQuery, [planId]);

      if (!planDetails.length) {
        return res.status(404).json({
          message: "No plan details found for the employer profile.",
        });
      }

      const employerNameQuery = `SELECT emp_name, profile_picture FROM employer WHERE id = ?;`;
      const [employerResult] = await conn.query(employerNameQuery, [emp_id]);

      if (!employerResult.length) {
        return res.status(404).json({
          message: "Employer not found. Please log in again.",
        });
      }

      const emp_name = employerResult[0].emp_name;
      const profile_picture = employerResult[0].profile_picture;

      const shortlistedCountQuery = `SELECT COUNT(*) AS shortlisted_count FROM employerprofiles WHERE emp_id = ? AND status = 'shortlisted';`;
      const [shortlistedCountResult] = await conn.query(shortlistedCountQuery, [
        emp_id,
      ]);
      const shortlisted_count = shortlistedCountResult[0].shortlisted_count;

      const unlockedCountQuery = `SELECT COUNT(*) AS unlocked_count FROM employerprofiles WHERE emp_id = ? AND status = 'unlocked';`;
      const [unlockedCountResult] = await conn.query(unlockedCountQuery, [
        emp_id,
      ]);
      const unlocked_count = unlockedCountResult[0].unlocked_count;

      res.status(200).json({
        message: "Employer profile plan fetched successfully.",
        data: {
          ...planDetails[0],
          emp_name,
          profile_picture,
          creditsLeft: planIdResult[0]?.creditsLeft || 0,
          end_date: planIdResult[0]?.end_date || 0,
          candidate_counts: {
            shortlisted_count: shortlisted_count,
            unlocked_count: unlocked_count,
          },
        },
      });
    } finally {
      conn.release();
    }
  } catch (error) {
    console.error("Error fetching profiles:", error);
    return res.status(500).json({ message: "Failed to fetch profiles." });
  }
};

// Get employer and company details
exports.getEmployerDetails = async (req, res) => {
  const emp_id = req.headers["authorization"];

  if (!emp_id) {
    return res
      .status(400)
      .json({ message: "Unauthorized Access, Please Log in Again." });
  }

  try {
    const conn = await pool.promise().getConnection();

    try {
      const employerIdQuery = `SELECT * FROM employer WHERE id = ?;`;
      const [employerIdResult] = await conn.query(employerIdQuery, [emp_id]);

      if (!employerIdResult.length) {
        return res.status(404).json({
          message: "No employer details Found. Please log in again.",
        });
      }

      const companyId = employerIdResult[0].company_id;

      const companyDetailsQuery = `SELECT * FROM company WHERE id = ?;`;
      const [companyDetails] = await conn.query(companyDetailsQuery, [
        companyId,
      ]);

      if (!companyDetails.length) {
        return res.status(404).json({
          message: "No plan details found for the employer profile.",
        });
      }

      res.status(200).json({
        message: "Employer profile data fetched successfully.",
        data: {
          ...employerIdResult[0],
          ...companyDetails[0],
        },
      });
    } finally {
      conn.release();
    }
  } catch (error) {
    console.error("Error fetching Employer Data:", error);
    return res.status(500).json({ message: "Failed to fetch Employer Data." });
  }
};