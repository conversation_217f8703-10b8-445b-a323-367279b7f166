import React, { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { useNavigate } from "react-router-dom";
import { HiOutlineSparkles, HiOutlineBriefcase } from "react-icons/hi";
import {
  Header,
  StatsOverview,
  JobList,
  ProfileSkelLoader,
  JobDescriptionModal,
} from "./index";
import StatCard from "../components/StatCard";
import PositionsCard from "../components/PositionsCard";
import ProfileCard from "../ProfilesUI/ProfileCard";
import toast from "react-hot-toast";

const EmployerDashboard = () => {
  const jstoken = Cookies.get("jstoken");
  const emp_id = Cookies.get("employerId");
  const navigate = useNavigate();
  const [shortListedCandidatesCount, setShortListedCandidatesCount] =
    useState(0);
  const [unlockedCandidatesCount, setUnlockedCandidatesCount] = useState(0);
  const [InterviewedCandidatesCount, setInterviewedCandidatesCount] =
    useState(0);
  const [offeredCandidatesCount, setOfferedCandidatesCount] = useState(0);
  const [empData, setEmpData] = useState({
    name: "Default User1",
    plan_name: "PRO",
    creditsLeft: 100,
  });

  const [isModalOpen, setModalOpen] = useState(false);
  const [jobData, setJobData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  // Job Description state
  const [dashboardJobDescription, setDashboardJobDescription] = useState(null);
  const [jdLoading, setJdLoading] = useState(false);
  const handleShortlist = async (id) => {
    if (loadingId === id) return;
    setLoadingId(id);

    try {
      const emp_id = Cookies.get("employerId");
      if (!emp_id) {
        toast.error("You need to be an employer to shortlist profiles");
        return;
      }

      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/shortlist-candidate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ emp_id, cand_id: id }),
        }
      );

      if (!response.ok) {
        const msg = await response.json();
        toast.error(msg.message);
        throw new Error(`HTTP error! status: ${msg.message}`);
      }

      const data = await response.json();
      toast.success(data.message);
      // Remove the shortlisted profile from jobData
      setJobData((prev) =>
        prev.filter((profile) => profile.video_profile_id !== id)
      );
    } catch (error) {
      console.error("Error during shortlist operation:", error);
    } finally {
      setLoadingId(null);
    }
  };
  const [loadingId, setLoadingId] = useState(null);

  useEffect(() => {
    let isMounted = true;

    const filterMatchingProfiles = (profiles, jobDescription) => {
      if (!profiles || !jobDescription) return [];

      return profiles
        .filter((profile) => {
          // Check role match (case-insensitive)
          const roleMatches =
            profile.role.toLowerCase() === jobDescription.role.toLowerCase();

          // Check skills match
          const profileSkills = profile.skills
            .toLowerCase()
            .split(",")
            .map((s) => s.trim());
          const jdSkills = Array.isArray(jobDescription.skills)
            ? jobDescription.skills.map((s) => s.toLowerCase())
            : jobDescription.skills
                .toLowerCase()
                .split(",")
                .map((s) => s.trim());

          const skillsMatch = jdSkills.some((skill) =>
            profileSkills.includes(skill)
          );

          return roleMatches && skillsMatch;
        })
        .sort((a, b) => {
          // Sort by number of matching skills
          const aSkills = a.skills
            .toLowerCase()
            .split(",")
            .map((s) => s.trim());
          const bSkills = b.skills
            .toLowerCase()
            .split(",")
            .map((s) => s.trim());
          const jdSkills = Array.isArray(jobDescription.skills)
            ? jobDescription.skills.map((s) => s.toLowerCase())
            : jobDescription.skills
                .toLowerCase()
                .split(",")
                .map((s) => s.trim());

          const aMatches = jdSkills.filter((skill) =>
            aSkills.includes(skill)
          ).length;
          const bMatches = jdSkills.filter((skill) =>
            bSkills.includes(skill)
          ).length;

          return bMatches - aMatches;
        });
    };

    const getAllProfiles = async () => {
      try {
        setIsLoading(true);

        if (!dashboardJobDescription) {
          setJobData([]);
          setIsLoading(false);
          return;
        }

        const data = await fetch(
          `${
            import.meta.env.VITE_APP_HOST
          }/api/v1/getSuggestedCandidates?emp_id=${emp_id}`
        );
        let res = await data.json();

        if (isMounted && res.candidateProfiles?.length) {
          const matchingProfiles = filterMatchingProfiles(
            res.candidateProfiles,
            dashboardJobDescription
          );
          setJobData(matchingProfiles);
        } else {
          setJobData([]);
        }
        setIsLoading(false);
      } catch (err) {
        console.error(`Error fetching profiles:`, err);
        if (isMounted) {
          setIsLoading(false);
          setJobData([]);
        }
      }
    };

    if (dashboardJobDescription) {
      getAllProfiles();
    }

    return () => {
      isMounted = false;
    };
  }, [emp_id, dashboardJobDescription]);

  useEffect(() => {
    const fetchCandidates = async () => {
      try {
        const profileData = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/employerProfilesData`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: emp_id,
            },
          }
        );
        const profileJson = await profileData.json();

        if (profileJson.data) {
          setShortListedCandidatesCount(
            profileJson?.data?.candidate_counts?.shortlisted_count || 0
          );
          setUnlockedCandidatesCount(
            profileJson?.data?.candidate_counts?.unlocked_count || 0
          );
          setInterviewedCandidatesCount(
            profileJson?.data?.candidate_counts?.interviewed_count || 0
          );
          setOfferedCandidatesCount(
            profileJson?.data?.candidate_counts?.offered_count || 0
          );
          setEmpData({
            name: profileJson?.data?.emp_name || "Default User",
            plan_name: profileJson?.data?.plan_name || "PRO",
            creditsLeft: profileJson?.data?.creditsLeft,
            profile_picture: profileJson?.data?.profile_picture || "",
          });
        }
      } catch (err) {
        console.error("Error fetching shortlisted profiles:", err);
      }
    };

    // Fetch job description for dashboard
    const fetchDashboardJobDescription = async () => {
      setJdLoading(true);
      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${emp_id}`
        );
        if (response.ok) {
          const data = await response.json();
          setDashboardJobDescription(data.jobDescription || null);
        } else if (response.status === 404) {
          // No job description found, do not log error
          setDashboardJobDescription(null);
        } else {
          // Only log unexpected errors
          console.error(
            "Failed to fetch job description:",
            response.statusText
          );
          setDashboardJobDescription(null);
        }
      } catch (err) {
        console.error("Failed to fetch job description:", err);
        setDashboardJobDescription(null);
      }
      setJdLoading(false);
    };

    fetchCandidates();
    fetchDashboardJobDescription();
  }, [emp_id, isModalOpen]);

  // Delete handler for dashboard JD
  const handleDashboardDeleteJD = async () => {
    if (!dashboardJobDescription?._id) return;
    setJdLoading(true);
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${
          dashboardJobDescription._id
        }`,
        { method: "DELETE" }
      );
      if (response.ok) {
        toast.success("Job description deleted");
        setDashboardJobDescription(null);
      } else {
        toast.error("Failed to delete job description");
      }
    } catch {
      toast.error("Failed to delete job description");
    }
    setJdLoading(false);
  };

  return (
    <>
      {jstoken ? (
        <div className="mt-2 grid grid-cols-12 gap-x-4 gap-y-4">
          {/* Row 1: Stats and User Info */}
          <div className="card col-span-full rounded-2xl bg-white px-6 py-4 shadow-lg dark:bg-navy-700 dark:text-white">
            <div className="grid grid-cols-12 items-center gap-6">
              <Header empData={empData} />
              <StatsOverview
                shortListedCandidatesCount={shortListedCandidatesCount}
                unlockedCandidatesCount={unlockedCandidatesCount}
                InterviewedCandidatesCount={InterviewedCandidatesCount}
                offeredCandidatesCount={offeredCandidatesCount}
                navigate={navigate}
              />
              <div className="col-span-10 ml-8 flex w-full flex-col items-center rounded-lg bg-white p-0 dark:bg-navy-700 md:col-span-8 md:ml-20 md:p-4 lg:col-span-4  lg:ml-0">
                <h3 className="w-full text-center text-sm font-semibold text-gray-800 dark:text-white sm:text-lg lg:text-left">
                  Match Candidates on JD
                </h3>
                <p className="mb-2 text-center text-[14px] text-gray-500 dark:text-gray-300 sm:text-sm md:text-left">
                  Upload a job description to match suitable candidates.
                </p>
                <button
                  className="-ml-0 flex flex-row items-center justify-center rounded-lg bg-brand-500 px-2 py-2 text-[12px] text-white transition hover:bg-brand-600 dark:bg-brand-600 dark:hover:bg-brand-700 sm:text-base lg:-ml-[70px] xl:-ml-[90px]"
                  onClick={() => setModalOpen(true)}
                >
                  <HiOutlineSparkles className="mr-1 text-[13px] sm:text-lg" />{" "}
                  Upload Job Description
                </button>
              </div>
            </div>
            {/* Job Description Display Section */}
            <div className="mt-6">
              {jdLoading ? (
                <div className="text-center text-gray-500 dark:text-gray-300">
                  Loading job description...
                </div>
              ) : dashboardJobDescription ? (
                <div className="rounded-lg border border-gray-300 bg-gray-50 p-4 dark:bg-navy-800">
                  <h4 className="text-md mb-2 font-semibold text-gray-800 dark:text-white">
                    Current Job Description
                  </h4>
                  <div className="mb-1">
                    <span className="font-medium">Role:</span>{" "}
                    {dashboardJobDescription.role || "—"}
                  </div>
                  <div className="mb-1">
                    <span className="font-medium">Experience:</span>{" "}
                    {dashboardJobDescription.experience || "—"}
                  </div>
                  <div className="mb-1">
                    <span className="font-medium">Location:</span>{" "}
                    {dashboardJobDescription.location || "—"}
                  </div>
                  <div className="mb-1">
                    <span className="font-medium">Skills:</span>{" "}
                    {Array.isArray(dashboardJobDescription.skills)
                      ? dashboardJobDescription.skills.length
                        ? dashboardJobDescription.skills.join(", ")
                        : "—"
                      : dashboardJobDescription.skills || "—"}
                  </div>
                  <button
                    className="mt-3 rounded bg-red-500 px-3 py-1 text-white hover:bg-red-600"
                    onClick={handleDashboardDeleteJD}
                  >
                    Delete
                  </button>
                </div>
              ) : (
                <div className="text-center text-gray-500 dark:text-gray-300">
                  No job description found.
                </div>
              )}
            </div>
          </div>
          <div className="col-span-full grid grid-cols-12 gap-4">
            <JobDescriptionModal
              isOpen={isModalOpen}
              onClose={() => setModalOpen(false)}
            />
            {/* Profiles Section */}
            <div className="col-span-full">
              {!dashboardJobDescription ? (
                <div className="p-8 text-center">
                  <h3 className="mb-4 text-xl font-semibold text-gray-800 dark:text-white">
                    Matching Profiles
                  </h3>
                  <div className="mb-4 text-gray-500 dark:text-gray-400">
                    Please upload a job description to see matching profiles
                  </div>
                </div>
              ) : (
                <>
                  <h3 className="mb-4 text-xl font-semibold text-gray-800 dark:text-white">
                    Matching Profiles{" "}
                    {jobData.length > 0 && `(${jobData.length})`}
                  </h3>
                  {isLoading ? (
                    <ProfileSkelLoader />
                  ) : jobData.length > 0 ? (
                    <div className="grid gap-4 md:grid-cols-2">
                      {jobData.map((profile, index) => (
                        <ProfileCard
                          key={profile.id || index}
                          {...profile}
                          score={profile.score}
                          onShortlist={() =>
                            handleShortlist(profile.video_profile_id)
                          }
                          isLoading={loadingId === profile.video_profile_id}
                        />
                      ))}
                    </div>
                  ) : dashboardJobDescription ? (
                    <div className="text-center text-gray-500 dark:text-gray-400">
                      No matching profiles found for the current job
                      description.
                    </div>
                  ) : (
                    <div className="text-center text-gray-500 dark:text-gray-400">
                      Upload a job description to see matching profiles.
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      ) : (
        <h2>lol signin</h2>
      )}
    </>
  );
};

export default EmployerDashboard;
