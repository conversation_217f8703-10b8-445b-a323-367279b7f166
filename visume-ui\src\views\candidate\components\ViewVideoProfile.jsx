import Loader from "../../../components/Loader";
import React, { useDeferredValue, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { BsExclamationCircle } from "react-icons/bs";
import { useNavigate, useParams } from "react-router-dom";

function ViewVideoProfile() {
  const navigate = useNavigate();
  const { id } = useParams();
  const [data, setData] = useState(null); // use `null` to make checking for data easier
  const [loading, setLoading] = useState(true); // default loading is true
  const [toolTip, showToolTip] = useState(false);
  const [videoProfileId, setVideoProfileId] = useState("");

  const getProfileData = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume-data/${videoProfileId}`, // Update this to your correct API URL
        {
          method: "GET",
        }
      );

      if (!response.ok) {
        navigate("/candidate/video-resume");
        throw new Error(response.statusText);
      }

      const res = await response.json();
      setData(res.data);
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false); // Ensure the loading state is set to false after fetching
    }
  };

  const handleDelete = async () => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume/${videoProfileId}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        toast.success("Video profile deleted successfully");
        navigate("/candidate/video-resume");
        window.location.reload(); // Ensure the page is reloaded to reflect changes
      } else {
        console.error("Failed to delete video profile");
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  useEffect(() => {
    if (id) {
      setVideoProfileId(id);
    }
  }, [id]);

  useEffect(() => {
    if (videoProfileId) {
      getProfileData();
    }
  }, [videoProfileId]);

  if (loading) {
    return <Loader text={"Getting Video Profile Data"} />;
  }

  if (!data) {
    return <div>No video profile data found.</div>;
  }

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "N/A";
    const options = { year: "numeric", month: "long", day: "numeric" };
    return date.toLocaleDateString(undefined, options);
  };

  return (
    <div className="flex h-full min-h-screen w-full flex-col items-center justify-start bg-gray-50 py-6 dark:bg-gray-900 lg:px-6">
      <div className="flex h-full w-full flex-col rounded-xl bg-white p-6 shadow-lg dark:bg-gray-800">
         {/* Back Button */}
  <div className="flex items-center cursor-pointer space-x-2 mb-4">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="h-5 w-5 text-gray-700 dark:text-gray-300"
      viewBox="0 0 20 20"
      fill="currentColor"
    >
      <path
        fillRule="evenodd"
        d="M7.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 111.414 1.414L5.414 9H17a1 1 0 110 2H5.414l2.293 2.293a1 1 0 010 1.414z"
        clipRule="evenodd"
      />
    </svg>
    <span 
      className="text-gray-700 dark:text-gray-300 text-lg font-semibold cursor-pointer" 
      onClick={() => navigate("/candidate/dashboard")}
    >
      Back
    </span>
  </div>
        <span className=" mb-5 text-2xl font-bold text-gray-800 dark:text-gray-200">
          Review Video Resume
        </span>
        <div className="flex h-full w-full flex-col gap-6 lg:flex-row">
          <div className="flex w-full flex-col items-start lg:w-3/5">
            <div className="relative h-[60vh] overflow-hidden rounded-xl bg-gray-800 shadow-md">
              <video
                src={data?.video_url}
                className="h-full w-full object-cover"
                controls
              />
            </div>
            <div className="mt-5 rounded-xl w-full bg-white p-4 shadow-md dark:bg-gray-700">
          <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200">
            Overview
          </h3>
          <p className="mt-2 text-md text-gray-600 dark:text-gray-400">
            This page contains the scores of the interview taken and evaluated
            by AI. The AI has assessed the candidate’s skill, communication, and
            overall performance.
          </p>
        </div>
          </div>
          <div className="-mt-12 flex w-full flex-col space-y-4 rounded-xl bg-white p-6 shadow-md dark:bg-gray-700 lg:w-2/5 min-h-[60vh]">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
              Created At:{" "}
              <span className="text-md font-normal text-gray-700 dark:text-gray-300">
                {formatDate(data.created_at)}
              </span>
            </h3>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
              Role:{" "}
              <span className="text-md font-normal text-gray-700 dark:text-gray-300">
                {data.role}
              </span>
            </h3>
            <div className="flex flex-col space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                Your Scores
              </h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                {/* Skill Score */}
                <div
                  className={`relative flex flex-col items-center justify-center rounded-lg p-4 shadow-lg transition-transform transform hover:scale-105 duration-300 ease-in-out ${
                    JSON.parse(data.score).score.Skill_Score < 5
                      ? 'bg-gradient-to-tr from-red-100 to-red-200 dark:from-red-700 dark:to-red-800'
                      : JSON.parse(data.score).score.Skill_Score <= 7
                      ? 'bg-gradient-to-tr from-yellow-100 to-yellow-200 dark:from-yellow-700 dark:to-yellow-800'
                      : 'bg-gradient-to-tr from-green-100 to-green-200 dark:from-green-700 dark:to-green-800'
                  }`}
                >
                  <span className="text-lg font-bold text-gray-800 dark:text-gray-200">Skill</span>
                  <span
                    className={`text-4xl font-bold ${
                      JSON.parse(data.score).score.Skill_Score < 5
                        ? 'text-red-600 dark:text-red-400'
                        : JSON.parse(data.score).score.Skill_Score <= 7
                        ? 'text-yellow-600 dark:text-yellow-400'
                        : 'text-green-600 dark:text-green-400'
                    }`}
                  >
                    {JSON.parse(data.score).score.Skill_Score || 0}
                  </span>
                  {/* Animated Background Accent */}
                  <div
                    className={`absolute bottom-0 left-0 w-full h-1 ${
                      JSON.parse(data.score).score.Skill_Score < 5
                        ? 'bg-red-400 dark:bg-red-500'
                        : JSON.parse(data.score).score.Skill_Score <= 7
                        ? 'bg-yellow-400 dark:bg-yellow-500'
                        : 'bg-green-400 dark:bg-green-500'
                    } transition-width duration-500 ease-in-out`}
                  ></div>
                </div>

                {/* Communication Score */}
                <div
                  className={`relative flex flex-col items-center justify-center rounded-lg p-4 shadow-lg transition-transform transform hover:scale-105 duration-300 ease-in-out ${
                    JSON.parse(data.score).score.Communication_Score < 5
                      ? 'bg-gradient-to-tr from-red-100 to-red-200 dark:from-red-700 dark:to-red-800'
                      : JSON.parse(data.score).score.Communication_Score <= 7
                      ? 'bg-gradient-to-tr from-yellow-100 to-yellow-200 dark:from-yellow-700 dark:to-yellow-800'
                      : 'bg-gradient-to-tr from-green-100 to-green-200 dark:from-green-700 dark:to-green-800'
                  }`}
                >
                  <span className="text-lg font-bold text-gray-800 dark:text-gray-200">Communication</span>
                  <span
                    className={`text-4xl font-bold ${
                      JSON.parse(data.score).score.Communication_Score < 5
                        ? 'text-red-600 dark:text-red-400'
                        : JSON.parse(data.score).score.Communication_Score <= 7
                        ? 'text-yellow-600 dark:text-yellow-400'
                        : 'text-green-600 dark:text-green-400'
                    }`}
                  >
                    {JSON.parse(data.score).score.Communication_Score || 0}
                  </span>
                  {/* Animated Background Accent */}
                  <div
                    className={`absolute bottom-0 left-0 w-full h-1 ${
                      JSON.parse(data.score).score.Communication_Score < 5
                        ? 'bg-red-400 dark:bg-red-500'
                        : JSON.parse(data.score).score.Communication_Score <= 7
                        ? 'bg-yellow-400 dark:bg-yellow-500'
                        : 'bg-green-400 dark:bg-green-500'
                    } transition-width duration-500 ease-in-out`}
                  ></div>
                </div>

                {/* Overall Score */}
                <div
                  className={`relative flex flex-col items-center justify-center rounded-lg p-4 shadow-lg transition-transform transform hover:scale-105 duration-300 ease-in-out ${
                    JSON.parse(data.score).score.Overall_Score < 5
                      ? 'bg-gradient-to-tr from-red-100 to-red-200 dark:from-red-700 dark:to-red-800'
                      : JSON.parse(data.score).score.Overall_Score <= 7
                      ? 'bg-gradient-to-tr from-yellow-100 to-yellow-200 dark:from-yellow-700 dark:to-yellow-800'
                      : 'bg-gradient-to-tr from-green-100 to-green-200 dark:from-green-700 dark:to-green-800'
                  }`}
                >
                  <span className="text-lg font-bold text-gray-800 dark:text-gray-200">Overall Score</span>
                  <span
                    className={`text-4xl font-bold ${
                      JSON.parse(data.score).score.Overall_Score < 5
                        ? 'text-red-600 dark:text-red-400'
                        : JSON.parse(data.score).score.Overall_Score <= 7
                        ? 'text-yellow-600 dark:text-yellow-400'
                        : 'text-green-600 dark:text-green-400'
                    }`}
                  >
                    {JSON.parse(data.score).score.Overall_Score || 0}
                  </span>
                  {/* Animated Background Accent */}
                  <div
                    className={`absolute bottom-0 left-0 w-full h-1 ${
                      JSON.parse(data.score).score.Overall_Score < 5
                        ? 'bg-red-400 dark:bg-red-500'
                        : JSON.parse(data.score).score.Overall_Score <= 7
                        ? 'bg-yellow-400 dark:bg-yellow-500'
                        : 'bg-green-400 dark:bg-green-500'
                    } transition-width duration-500 ease-in-out`}
                  ></div>
                </div>
              </div>
            </div>


  <div className="flex flex-col">
    <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
      Suggestions
    </h3>
    <p className="text-sm text-gray-600 dark:text-gray-400">
      {JSON.parse(data.score).Suggestions}
    </p>
  </div>

  <div className="group relative flex flex-col">
    <h3 className="flex items-center gap-2 text-lg font-semibold text-gray-800 dark:text-gray-200">
      Status
      <BsExclamationCircle
        onMouseEnter={() => showToolTip(true)}
        onMouseLeave={() => showToolTip(false)}
        className="cursor-pointer"
      />
      {toolTip && (
        <div className="absolute bottom-full left-0 mb-2 block w-max rounded-md bg-gray-800 bg-opacity-50 px-3 py-1 text-sm text-white">
          Status will be Active if score is above 5, otherwise Inactive.
        </div>
      )}
    </h3>
    <input
      type="text"
      disabled
      value={data.status}
      className="mt-2 rounded-lg border p-2 dark:bg-gray-800 dark:text-gray-300"
    />
  </div>

  <div className="mt-12">
    <a
      className="inline-flex cursor-pointer items-center rounded-lg bg-red-500 px-3 py-3 font-semibold text-white shadow-md hover:bg-red-600"
      onClick={handleDelete}
    >
      <svg
        className="mr-3 h-6 w-6"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
      >
        <path
          fill="#FFFFFF"
          d="M17 10.5v-2c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v7c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2v-2l4 2v-7l-4 2Z"
        />
      </svg>
      Delete Video Resume
    </a>
  </div>
</div>

        </div>
        {/* OverView */}
       
      </div>
    </div>
  );
}

export default ViewVideoProfile;
