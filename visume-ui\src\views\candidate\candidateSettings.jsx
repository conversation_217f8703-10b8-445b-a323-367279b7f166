import { useEffect, useState } from "react";
import {
  EyeIcon,
  EyeOffIcon,
  User,
  Lock,
  Bell,
  Shield,
  Upload,
  FileText,
  Check,
  Camera,
  Settings,
} from "lucide-react";
import Cookies from "js-cookie";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";

const CandidateSettings = () => {
  const navigate = useNavigate();
  const cand_id = Cookies.get("candId");

  // Active tab state
  const [activeTab, setActiveTab] = useState("profile");

  // Profile state
  const [profileData, setProfileData] = useState({
    name: "",
    email: "",
    mobile: "",
    gender: "",
    language: "",
    location: "",
    profileImage: null,
  });

  // Password state
  const [passwordData, setPasswordData] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  // Notification state
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    jobAlerts: true,
    interviewReminders: true,
    marketingEmails: false,
  });

  // Privacy state
  const [privacySettings, setPrivacySettings] = useState({
    profileVisibility: "public",
    showContactInfo: true,
    allowDirectMessages: true,
  });

  // Loading states
  const [loading, setLoading] = useState({
    profile: false,
    password: false,
    notifications: false,
    privacy: false,
  });

  // Other states
  const [showPasswords, setShowPasswords] = useState({
    old: false,
    new: false,
    confirm: false,
  });
  const [uploadedFile, setUploadedFile] = useState(null);
  const [errors, setErrors] = useState({});
  const [isEditing, setIsEditing] = useState(false);

  // Fetch profile data
  useEffect(() => {
    const fetchProfileData = async () => {
      if (!cand_id) {
        toast.error("No Token Found, Please Login Again");
        navigate("/candidate/signIn");
        return;
      }

      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${cand_id}`
        );
        const data = await response.json();

        if (data.candidateProfile?.[0]) {
          const candidate = data.candidateProfile[0];
          console.log("Candidate profile API response:", candidate);
          setProfileData({
            name: candidate.cand_name || "",
            email: candidate.cand_email || "",
            mobile: candidate.cand_mobile || "",
            language:
              candidate.languages_known?.replace(/^\["|"\]$/g, "") || "",
            location:
              candidate.preferred_location?.replace(/^\["|"\]$/g, "") || "",
            profileImage: candidate.profile_picture || null,
          });
        }
      } catch (error) {
        console.error("Failed to fetch profile data:", error);
        toast.error("Failed to load profile data");
      }
    };

    fetchProfileData();
  }, [cand_id, navigate]);

  // Handle password change
  const handlePasswordChange = async () => {
    if (
      !passwordData.oldPassword ||
      !passwordData.newPassword ||
      !passwordData.confirmPassword
    ) {
      toast.error("Please fill in all password fields.");
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error("New passwords do not match.");
      return;
    }

    if (passwordData.newPassword.length < 8) {
      toast.error("Password must be at least 8 characters long.");
      return;
    }

    setLoading((prev) => ({ ...prev, password: true }));

    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/changePassword`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            cand_id,
            password: passwordData.oldPassword,
            newPassword: passwordData.newPassword,
          }),
        }
      );

      const responseData = await response.json();

      if (!response.ok) {
        toast.error(responseData.message);
      } else {
        setPasswordData({
          oldPassword: "",
          newPassword: "",
          confirmPassword: "",
        });
        toast.success("Password updated successfully!");
      }
    } catch (error) {
      toast.error("An unexpected error occurred.");
      console.error("Error:", error);
    } finally {
      setLoading((prev) => ({ ...prev, password: false }));
    }
  };

  // Handle profile update
  const handleProfileUpdate = async () => {
    setLoading((prev) => ({ ...prev, profile: true }));

    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${cand_id}`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            cand_name: profileData.name,
            cand_email: profileData.email,
            cand_mobile: profileData.mobile,
            languages_known: profileData.language,
            preferred_location: profileData.location,
            profile_picture: profileData.profileImage,
          }),
        }
      );
      const data = await response.json();
      if (!response.ok) {
        toast.error(data.message || "Failed to update profile");
      } else {
        toast.success("Profile updated successfully!");
        setIsEditing(false);
      }
    } catch (error) {
      toast.error("Failed to update profile");
    } finally {
      setLoading((prev) => ({ ...prev, profile: false }));
    }
  };

  // Handle file upload
  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      const isPdf = file.type === "application/pdf";
      if (isPdf && file.size <= 5 * 1024 * 1024) {
        // 5MB limit
        setUploadedFile(file);
        setErrors((prev) => ({ ...prev, resume: "" }));

        // Upload resume to server
        const formData = new FormData();
        formData.append("resume", file);
        formData.append("cand_id", cand_id);

        setLoading((prev) => ({ ...prev, profile: true }));
        try {
          const response = await fetch(
            `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/uploadResume`,
            {
              method: "POST",
              body: formData,
            }
          );
          const data = await response.json();
          if (!response.ok) {
            setErrors((prev) => ({
              ...prev,
              resume: data.message || "Resume upload failed.",
            }));
            setUploadedFile(null);
          } else {
            toast.success("Resume uploaded successfully!");
          }
        } catch (error) {
          setErrors((prev) => ({
            ...prev,
            resume: "Resume upload failed.",
          }));
          setUploadedFile(null);
        } finally {
          setLoading((prev) => ({ ...prev, profile: false }));
        }
      } else {
        setErrors((prev) => ({
          ...prev,
          resume: "Please upload a valid PDF file under 5MB.",
        }));
        setUploadedFile(null);
      }
    }
  };

  // Handle profile image upload
  const handleProfileImageUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.type.startsWith("image/") && file.size <= 2 * 1024 * 1024) {
        // 2MB limit
        const formData = new FormData();
        formData.append("profile_picture", file);
        formData.append("cand_id", cand_id);

        setLoading((prev) => ({ ...prev, profile: true }));
        try {
          const response = await fetch(
            `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/uploadProfileImage`,
            {
              method: "POST",
              body: formData,
            }
          );
          const data = await response.json();
          if (!response.ok) {
            toast.error(data.message || "Profile image upload failed.");
          } else {
            setProfileData((prev) => ({
              ...prev,
              profileImage: data.profile_picture_url || prev.profileImage,
            }));
            toast.success("Profile image updated!");
          }
        } catch (error) {
          toast.error("Profile image upload failed.");
        } finally {
          setLoading((prev) => ({ ...prev, profile: false }));
        }
      } else {
        toast.error("Please upload a valid image file under 2MB.");
      }
    }
  };

  // Tab configuration
  const tabs = [
    { id: "profile", label: "Profile", icon: User },
    { id: "security", label: "Security", icon: Lock },
    { id: "notifications", label: "Notifications", icon: Bell },
    { id: "privacy", label: "Privacy", icon: Shield },
  ];

  return (
    <div className="p-4">
      {/* Header */}
      <div className="mb-6 rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-900">
        <div className="p-6">
          <div className="flex items-center gap-3">
            <div className="rounded-lg bg-gradient-to-br from-gray-100 to-blue-100 p-2 dark:from-gray-900/50 dark:to-blue-900/50">
              <Settings className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                Settings
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Manage your account and preferences
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-gray-900">
        <div className="p-6">
          {/* Tab Navigation */}
          <div className="mb-8 border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 border-b-2 px-3 py-2 text-sm font-medium transition-colors duration-200 ${
                      activeTab === tab.id
                        ? "border-blue-500 text-blue-600 dark:text-blue-400"
                        : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Content Area */}
          <div>
            {/* Profile Tab */}
            {activeTab === "profile" && (
              <div>
                <div className="mb-8 flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                      Profile Information
                    </h2>
                    <p className="mt-1 text-gray-600 dark:text-gray-400">
                      Update your personal information and profile settings
                    </p>
                  </div>
                  <button
                    onClick={() =>
                      isEditing ? handleProfileUpdate() : setIsEditing(true)
                    }
                    disabled={loading.profile}
                    className="inline-flex items-center gap-2 rounded-lg bg-blue-600 px-6 py-2 font-medium text-white transition-colors duration-200 hover:bg-blue-700 disabled:bg-blue-400"
                  >
                    {loading.profile ? (
                      <>
                        <div className="border-t-transparent h-4 w-4 animate-spin rounded-full border-2 border-white"></div>
                        Saving...
                      </>
                    ) : isEditing ? (
                      <>
                        <Check className="h-4 w-4" />
                        Save Changes
                      </>
                    ) : (
                      "Edit Profile"
                    )}
                  </button>
                </div>

                {/* Profile Image */}
                <div className="mb-8 flex items-center gap-6 border-b border-gray-200 pb-8 dark:border-gray-700">
                  <div className="relative">
                    <img
                      src={
                        profileData.profileImage
                          ? profileData.profileImage.startsWith("http")
                            ? profileData.profileImage
                            : `${import.meta.env.VITE_APP_HOST}/${profileData.profileImage.replace(/^\/+/, "")}`
                          : "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                      }
                      alt="Profile"
                      className="h-24 w-24 rounded-full border-4 border-white object-cover shadow-lg dark:border-gray-700"
                    />
                    {isEditing && (
                      <label className="bg-black absolute inset-0 flex cursor-pointer items-center justify-center rounded-full bg-opacity-50 opacity-0 transition-opacity duration-200 hover:opacity-100">
                        <Camera className="h-6 w-6 text-white" />
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleProfileImageUpload}
                          className="hidden"
                        />
                      </label>
                    )}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {profileData.name}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      {profileData.email}
                    </p>
                    {isEditing && (
                      <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                        Click on image to change profile photo
                      </p>
                    )}
                  </div>
                </div>

                {/* Profile Form */}
                <div className="space-y-6">
                  <div>
                    <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Full Name
                    </label>
                    <input
                      type="text"
                      value={profileData.name}
                      onChange={(e) =>
                        setProfileData((prev) => ({
                          ...prev,
                          name: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      className="focus:border-transparent w-full rounded-lg border border-gray-300 bg-white px-4 py-3 text-gray-900 transition-colors duration-200 focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:disabled:bg-gray-800"
                    />
                  </div>

                  <div>
                    <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Email Address
                    </label>
                    <p className="w-full rounded-lg border border-gray-300 bg-gray-50 px-4 py-3 text-gray-900 dark:border-gray-600 dark:bg-gray-800 dark:text-white">
                      {profileData.email}
                    </p>
                  </div>

                  <div>
                    <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Preferred Location
                    </label>
                    <input
                      type="text"
                      value={profileData.location}
                      onChange={(e) =>
                        setProfileData((prev) => ({
                          ...prev,
                          location: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      placeholder="e.g., New York, Remote, Bangalore"
                      className="focus:border-transparent w-full rounded-lg border border-gray-300 bg-white px-4 py-3 text-gray-900 transition-colors duration-200 focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:disabled:bg-gray-800"
                    />
                  </div>

                  <div>
                    <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Mobile Number
                    </label>
                    <input
                      type="tel"
                      value={profileData.mobile}
                      onChange={(e) =>
                        setProfileData((prev) => ({
                          ...prev,
                          mobile: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      className="focus:border-transparent w-full rounded-lg border border-gray-300 bg-white px-4 py-3 text-gray-900 transition-colors duration-200 focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:disabled:bg-gray-800"
                    />
                  </div>

                  <div>
                    <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Languages Known
                    </label>
                    <input
                      type="text"
                      value={profileData.language}
                      onChange={(e) =>
                        setProfileData((prev) => ({
                          ...prev,
                          language: e.target.value,
                        }))
                      }
                      disabled={!isEditing}
                      placeholder="e.g., English, Hindi, Spanish"
                      className="focus:border-transparent w-full rounded-lg border border-gray-300 bg-white px-4 py-3 text-gray-900 transition-colors duration-200 focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:disabled:bg-gray-800"
                    />
                  </div>
                </div>

                {/* Resume Section */}
                <div className="mt-8 border-t border-gray-200 pt-8 dark:border-gray-700">
                  <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
                    Resume
                  </h3>
                  <div className="flex items-center gap-4">
                    <a
                      href="/resume"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-2 rounded-lg bg-gray-100 px-4 py-2 font-medium text-gray-700 transition-colors duration-200 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                    >
                      <FileText className="h-4 w-4" />
                      View Current Resume
                    </a>

                    {isEditing && (
                      <div>
                        <label className="inline-flex cursor-pointer items-center gap-2 rounded-lg bg-blue-600 px-4 py-2 font-medium text-white transition-colors duration-200 hover:bg-blue-700">
                          <Upload className="h-4 w-4" />
                          Upload New Resume
                          <input
                            type="file"
                            accept=".pdf,.doc,.docx"
                            onChange={handleFileUpload}
                            className="hidden"
                          />
                        </label>
                        {uploadedFile && (
                          <p className="mt-2 text-sm text-green-600 dark:text-green-400">
                            ✓ {uploadedFile.name} uploaded successfully
                          </p>
                        )}
                        {errors.resume && (
                          <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                            {errors.resume}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Security Tab */}
            {activeTab === "security" && (
              <div>
                <div className="mb-8">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    Security Settings
                  </h2>
                  <p className="mt-1 text-gray-600 dark:text-gray-400">
                    Update your password and security preferences
                  </p>
                </div>

                <div className="max-w-2xl">
                  <h3 className="mb-6 text-lg font-semibold text-gray-900 dark:text-white">
                    Change Password
                  </h3>

                  <div className="space-y-6">
                    <div>
                      <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Current Password
                      </label>
                      <div className="relative">
                        <input
                          type={showPasswords.old ? "text" : "password"}
                          value={passwordData.oldPassword}
                          onChange={(e) =>
                            setPasswordData((prev) => ({
                              ...prev,
                              oldPassword: e.target.value,
                            }))
                          }
                          className="focus:border-transparent w-full rounded-lg border border-gray-300 bg-white px-4 py-3 pr-12 text-gray-900 transition-colors duration-200 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                          placeholder="Enter your current password"
                        />
                        <button
                          type="button"
                          onClick={() =>
                            setShowPasswords((prev) => ({
                              ...prev,
                              old: !prev.old,
                            }))
                          }
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                        >
                          {showPasswords.old ? (
                            <EyeOffIcon className="h-5 w-5" />
                          ) : (
                            <EyeIcon className="h-5 w-5" />
                          )}
                        </button>
                      </div>
                    </div>

                    <div>
                      <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                        New Password
                      </label>
                      <div className="relative">
                        <input
                          type={showPasswords.new ? "text" : "password"}
                          value={passwordData.newPassword}
                          onChange={(e) =>
                            setPasswordData((prev) => ({
                              ...prev,
                              newPassword: e.target.value,
                            }))
                          }
                          className="focus:border-transparent w-full rounded-lg border border-gray-300 bg-white px-4 py-3 pr-12 text-gray-900 transition-colors duration-200 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                          placeholder="Enter a new password"
                        />
                        <button
                          type="button"
                          onClick={() =>
                            setShowPasswords((prev) => ({
                              ...prev,
                              new: !prev.new,
                            }))
                          }
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                        >
                          {showPasswords.new ? (
                            <EyeOffIcon className="h-5 w-5" />
                          ) : (
                            <EyeIcon className="h-5 w-5" />
                          )}
                        </button>
                      </div>
                    </div>

                    <div>
                      <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Confirm New Password
                      </label>
                      <div className="relative">
                        <input
                          type={showPasswords.confirm ? "text" : "password"}
                          value={passwordData.confirmPassword}
                          onChange={(e) =>
                            setPasswordData((prev) => ({
                              ...prev,
                              confirmPassword: e.target.value,
                            }))
                          }
                          className="focus:border-transparent w-full rounded-lg border border-gray-300 bg-white px-4 py-3 pr-12 text-gray-900 transition-colors duration-200 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                          placeholder="Confirm your new password"
                        />
                        <button
                          type="button"
                          onClick={() =>
                            setShowPasswords((prev) => ({
                              ...prev,
                              confirm: !prev.confirm,
                            }))
                          }
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                        >
                          {showPasswords.confirm ? (
                            <EyeOffIcon className="h-5 w-5" />
                          ) : (
                            <EyeIcon className="h-5 w-5" />
                          )}
                        </button>
                      </div>
                    </div>

                    <button
                      onClick={handlePasswordChange}
                      disabled={loading.password}
                      className="inline-flex items-center gap-2 rounded-lg bg-blue-600 px-6 py-3 font-medium text-white transition-colors duration-200 hover:bg-blue-700 disabled:bg-blue-400"
                    >
                      {loading.password ? (
                        <>
                          <div className="border-t-transparent h-4 w-4 animate-spin rounded-full border-2 border-white"></div>
                          Updating...
                        </>
                      ) : (
                        "Update Password"
                      )}
                    </button>
                  </div>

                  {/* Security Tips */}
                  <div className="mt-8 rounded-lg border border-blue-200 bg-blue-50 p-6 dark:border-blue-800 dark:bg-blue-900/20">
                    <h4 className="mb-3 text-sm font-medium text-blue-900 dark:text-blue-300">
                      Password Security Tips
                    </h4>
                    <ul className="space-y-2 text-sm text-blue-800 dark:text-blue-400">
                      <li className="flex items-start gap-2">
                        <Check className="mt-0.5 h-4 w-4 flex-shrink-0" />
                        Use at least 8 characters with a mix of letters,
                        numbers, and symbols
                      </li>
                      <li className="flex items-start gap-2">
                        <Check className="mt-0.5 h-4 w-4 flex-shrink-0" />
                        Avoid using personal information like names or birthdays
                      </li>
                      <li className="flex items-start gap-2">
                        <Check className="mt-0.5 h-4 w-4 flex-shrink-0" />
                        Don't reuse passwords from other accounts
                      </li>
                      <li className="flex items-start gap-2">
                        <Check className="mt-0.5 h-4 w-4 flex-shrink-0" />
                        Consider using a password manager for better security
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {/* Notifications Tab */}
            {activeTab === "notifications" && (
              <div>
                <div className="mb-8">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    Notification Preferences
                  </h2>
                  <p className="mt-1 text-gray-600 dark:text-gray-400">
                    Manage how you receive notifications and updates
                  </p>
                </div>

                <div className="space-y-6">
                  {[
                    {
                      key: "emailNotifications",
                      label: "Email Notifications",
                      description: "Receive notifications via email",
                    },
                    {
                      key: "jobAlerts",
                      label: "Job Alerts",
                      description: "Get notified about new job opportunities",
                    },
                    {
                      key: "interviewReminders",
                      label: "Interview Reminders",
                      description:
                        "Receive reminders about upcoming interviews",
                    },
                    {
                      key: "marketingEmails",
                      label: "Marketing Emails",
                      description: "Receive promotional emails and newsletters",
                    },
                  ].map((setting) => (
                    <div
                      key={setting.key}
                      className="flex items-center justify-between rounded-lg border border-gray-200 p-4 dark:border-gray-700"
                    >
                      <div>
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                          {setting.label}
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {setting.description}
                        </p>
                      </div>
                      <label className="relative inline-flex cursor-pointer items-center">
                        <input
                          type="checkbox"
                          checked={notificationSettings[setting.key]}
                          onChange={(e) =>
                            setNotificationSettings((prev) => ({
                              ...prev,
                              [setting.key]: e.target.checked,
                            }))
                          }
                          className="peer sr-only"
                        />
                        <div className="peer h-6 w-11 rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-blue-600 peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-blue-800"></div>
                      </label>
                    </div>
                  ))}
                </div>

                <div className="mt-8">
                  <button
                    onClick={async () => {
                      setLoading((prev) => ({ ...prev, notifications: true }));
                      try {
                        const response = await fetch(
                          `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${cand_id}/notifications`,
                          {
                            method: "PUT",
                            headers: { "Content-Type": "application/json" },
                            body: JSON.stringify(notificationSettings),
                          }
                        );
                        const data = await response.json();
                        if (!response.ok) {
                          toast.error(data.message || "Failed to update notifications");
                        } else {
                          toast.success("Notification preferences updated!");
                        }
                      } catch (error) {
                        toast.error("Failed to update notifications");
                      } finally {
                        setLoading((prev) => ({ ...prev, notifications: false }));
                      }
                    }}
                    disabled={loading.notifications}
                    className="inline-flex items-center gap-2 rounded-lg bg-blue-600 px-6 py-3 font-medium text-white transition-colors duration-200 hover:bg-blue-700 disabled:bg-blue-400"
                  >
                    {loading.notifications ? (
                      <>
                        <div className="border-t-transparent h-4 w-4 animate-spin rounded-full border-2 border-white"></div>
                        Saving...
                      </>
                    ) : (
                      "Save Preferences"
                    )}
                  </button>
                </div>
              </div>
            )}

            {/* Privacy Tab */}
            {activeTab === "privacy" && (
              <div>
                <div className="mb-8">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    Privacy Settings
                  </h2>
                  <p className="mt-1 text-gray-600 dark:text-gray-400">
                    Control who can see your information and how it's used
                  </p>
                </div>

                <div className="space-y-8">
                  <div>
                    <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
                      Profile Visibility
                    </h3>
                    <div className="space-y-3">
                      {[
                        {
                          value: "public",
                          label: "Public",
                          description: "Anyone can view your profile",
                        },
                        {
                          value: "employers",
                          label: "Employers Only",
                          description:
                            "Only verified employers can view your profile",
                        },
                        {
                          value: "private",
                          label: "Private",
                          description: "Only you can view your profile",
                        },
                      ].map((option) => (
                        <label
                          key={option.value}
                          className="flex cursor-pointer items-center rounded-lg border border-gray-200 p-4 transition-colors duration-200 hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-700/50"
                        >
                          <input
                            type="radio"
                            name="profileVisibility"
                            value={option.value}
                            checked={
                              privacySettings.profileVisibility === option.value
                            }
                            onChange={(e) =>
                              setPrivacySettings((prev) => ({
                                ...prev,
                                profileVisibility: e.target.value,
                              }))
                            }
                            className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600"
                          />
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {option.label}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {option.description}
                            </div>
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-4">
                    {[
                      {
                        key: "showContactInfo",
                        label: "Show Contact Information",
                        description:
                          "Allow others to see your email and phone number",
                      },
                      {
                        key: "allowDirectMessages",
                        label: "Allow Direct Messages",
                        description: "Let employers send you messages directly",
                      },
                    ].map((setting) => (
                      <div
                        key={setting.key}
                        className="flex items-center justify-between rounded-lg border border-gray-200 p-4 dark:border-gray-700"
                      >
                        <div>
                          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                            {setting.label}
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {setting.description}
                          </p>
                        </div>
                        <label className="relative inline-flex cursor-pointer items-center">
                          <input
                            type="checkbox"
                            checked={privacySettings[setting.key]}
                            onChange={(e) =>
                              setPrivacySettings((prev) => ({
                                ...prev,
                                [setting.key]: e.target.checked,
                              }))
                            }
                            className="peer sr-only"
                          />
                          <div className="peer h-6 w-11 rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-blue-600 peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-blue-800"></div>
                        </label>
                      </div>
                    ))}
                  </div>

                  <div className="mt-8">
                    <button
                      onClick={async () => {
                        setLoading((prev) => ({ ...prev, privacy: true }));
                        try {
                          const response = await fetch(
                            `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${cand_id}/privacy`,
                            {
                              method: "PUT",
                              headers: { "Content-Type": "application/json" },
                              body: JSON.stringify(privacySettings),
                            }
                          );
                          const data = await response.json();
                          if (!response.ok) {
                            toast.error(data.message || "Failed to update privacy settings");
                          } else {
                            toast.success("Privacy settings updated!");
                          }
                        } catch (error) {
                          toast.error("Failed to update privacy settings");
                        } finally {
                          setLoading((prev) => ({ ...prev, privacy: false }));
                        }
                      }}
                      disabled={loading.privacy}
                      className="inline-flex items-center gap-2 rounded-lg bg-blue-600 px-6 py-3 font-medium text-white transition-colors duration-200 hover:bg-blue-700 disabled:bg-blue-400"
                    >
                      {loading.privacy ? (
                        <>
                          <div className="border-t-transparent h-4 w-4 animate-spin rounded-full border-2 border-white"></div>
                          Saving...
                        </>
                      ) : (
                        "Save Settings"
                      )}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CandidateSettings;
