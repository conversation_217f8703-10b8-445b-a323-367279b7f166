import React, { useState } from "react";
import Cookies from "js-cookie";
import toast from "react-hot-toast";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { HiOutlineCloudUpload, HiChevronLeft, HiChevronRight, HiX } from "react-icons/hi";

import { fetchJobRoles, fetchSkills } from "../../candidate/components/CreateVR/createVRApi";

const JobDescriptionModal = ({ isOpen, onClose, onJobDescriptionChange }) => {
  const [jobRoles, setJobRoles] = useState([]);
  const [skillsList, setSkillsList] = useState([]);

  // Fetch roles and skills on mount
  React.useEffect(() => {
    async function fetchData() {
      const roles = await fetchJobRoles();
      setJobRoles(roles || []);
      setFilteredRoles(roles || []);
      const skills = await fetchSkills();
      setSkillsList(skills || []);
      setFilteredSkills(skills || []);
    }
    fetchData();
    // eslint-disable-next-line
  }, []);

  const locations = ["Bangalore", "Delhi", "Mumbai", "Hyderabad"];
  const emp_id = Cookies.get("employerId");
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);
  const [fileName, setFileName] = useState("");
  const [role, setRole] = useState("");
  const [filteredRoles, setFilteredRoles] = useState([]);
  const [location, setLocation] = useState("");
  const [filteredLocations, setFilteredLocations] = useState(locations);
  const [skills, setSkills] = useState([]);
  const [filteredSkills, setFilteredSkills] = useState([]);
  const [experience, setExperience] = useState("");
  const [uploadedUrl, setUploadedUrl] = useState("");

  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      try {
        setLoading(true);

        // Create FormData and append the file
        const formData = new FormData();
        formData.append("job_description", file);

        // Make API call
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/upload-job-description/${emp_id}`,
          {
            method: "POST",
            body: formData,
          }
        );

        if (!response.ok) {
          const errorData = await response.json();
          toast.error(errorData.message || "Failed to upload job description");
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        if (data?.JobDescription) {
          setLocation(data.JobDescription.location?.[0] || "");
          setRole(data.JobDescription.role || "");
          setExperience(data.JobDescription.experience || "");
          setSkills(
            Array.isArray(data.JobDescription.skills)
              ? data.JobDescription.skills
              : JSON.parse(data.JobDescription.skills || "[]")
          );
          setUploadedUrl(data.filePath || "");
          toast.success(data.message);
          toast.success("Job description uploaded successfully!");
        } else {
          toast.error("Failed to extract necessary details from the file.");
        }
      } catch (err) {
        console.error(`Failed to upload job description: ${err}`);
        toast.error(
          err?.message ||
            "An error occurred while uploading the job description"
        );
      } finally {
        setLoading(false);
        setStep(2);
      }
    } else {
      toast.error("No file selected. Please choose a file to upload.");
    }
  };

  const handleSave = async () => {
    if (!role || !experience || !location || !skills.length) {
      toast.error("Please fill all fields and add at least one skill.");
      return;
    }
    // Debug: log values being sent
    console.log("Submitting JD:", { role, experience, location, skills, uploadedUrl, emp_id });
    setLoading(true);
    const jobDescription = { role, experience, location, skills };
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/create-job-description`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...jobDescription,
            filePath: uploadedUrl,
            emp_id: emp_id,
          }),
        }
      );

      if (!response.ok) {
        const msg = await response.json();
        toast.error(msg.message);
        throw new Error(`HTTP error! status: ${msg.message}`);
      }

      const data = await response.json();
      toast.success(data.message || "Job Description created successfully.");
      setStep(1);
      setRole("");
      setExperience("");
      setLocation("");
      setSkills([]);
      setUploadedUrl("");
      setFileName("");
    } catch (err) {
      console.error(`Failed to upload job description: ${err}`);
      toast.error(
        err?.message || "An error occurred while uploading the job description"
      );
    } finally {
      setLoading(false);
      onClose();
      if (onJobDescriptionChange) onJobDescriptionChange();
    }
  };

  // State for created job description
  const [createdJobDescription, setCreatedJobDescription] = useState(null);

  // Fetch created job description for employer (stub: assumes GET endpoint exists)
  React.useEffect(() => {
    async function fetchCreatedJobDescription() {
      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${emp_id}`
        );
        if (response.ok) {
          const data = await response.json();
          setCreatedJobDescription(data.jobDescription || null);
        }
      } catch (err) {
        // ignore error
      }
    }
    if (isOpen) fetchCreatedJobDescription();
    // eslint-disable-next-line
  }, [isOpen]);

  // Delete job description handler (stub: assumes DELETE endpoint exists)
  const handleDeleteJobDescription = async () => {
    if (!createdJobDescription?._id) return;
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${createdJobDescription._id}`,
        { method: "DELETE" }
      );
      if (response.ok) {
        toast.success("Job description deleted");
        setCreatedJobDescription(null);
      } else {
        toast.error("Failed to delete job description");
      }
    } catch {
      toast.error("Failed to delete job description");
    }
    if (onJobDescriptionChange) onJobDescriptionChange();
  };

  return (
    <div
      className={`fixed inset-0 z-50 bg-gray-500 bg-opacity-75 transition-opacity ${
        isOpen ? "block" : "hidden"
      }`}
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div className="flex min-h-screen items-center justify-center px-4 text-center">
        <div className="relative w-full max-w-lg overflow-hidden rounded-lg bg-white text-left shadow-xl dark:bg-navy-700">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-300 bg-gray-100 px-4 py-3 dark:border-navy-500 dark:bg-navy-800">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
              {step === 1 ? "Upload Job Description" : "Enter Job Details"}
            </h3>
            <button
              type="button"
              className="text-gray-500 dark:text-gray-400"
              onClick={onClose}
            >
              <HiX className="text-2xl" />
            </button>
          </div>

          {/* Body */}
          <div className="p-6 transition-transform duration-500 ease-in-out">

            {/* Step 1: Upload */}
            {step === 1 && (
              <div>
                <div className="flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-6">
                  <input
                    type="file"
                    id="file-upload"
                    className="hidden"
                    accept=".pdf,.doc,.docx"
                    onChange={handleFileUpload}
                  />
                  <label
                    htmlFor="file-upload"
                    className="flex cursor-pointer flex-col items-center justify-center text-center text-gray-600 dark:text-gray-300"
                  >
                    <HiOutlineCloudUpload className="mb-2 text-3xl" />
                    <span className="font-semibold">
                      Drag and drop your job description file here, or
                    </span>
                    <span className="font-semibold text-brand-500">
                      Browse files
                    </span>
                  </label>
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    Supported formats: PDF, DOC, DOCX
                  </p>
                </div>
                {fileName && (
                  <div className="mt-4 text-sm text-gray-600 dark:text-gray-300">
                    Selected file: <strong>{fileName}</strong>
                  </div>
                )}
                <div className="mt-4">
                  <button
                    type="button"
                    className="w-full rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
                    onClick={() => setStep(2)}
                  >
                    Fill Details Manually
                  </button>
                </div>
              </div>
            )}

            {/* Step 2: Manual Entry */}
            {step === 2 && (
              <div>
                {/* Role Input */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Role
                  </label>
                  <input
                    type="text"
                    className="mt-1 w-full rounded-md border-[1px] border-gray-300 p-2 focus:border-blue-500 focus:ring-blue-500 dark:bg-navy-600 dark:text-white"
                    placeholder="Enter the role"
                    value={role}
                    onChange={(e) => {
                      const input = e.target.value;
                      setRole(input);
                      setFilteredRoles(
                        jobRoles.filter((r) =>
                          r.toLowerCase().includes(input.toLowerCase())
                        )
                      );
                    }}
                  />
                  {role && (
                    <ul className="mt-2 max-h-32 overflow-auto rounded-md border border-gray-300 bg-white shadow-md dark:bg-navy-600">
                      {filteredRoles.map((suggestedRole, index) => (
                        <li
                          key={index}
                          className="cursor-pointer p-2 hover:bg-gray-100 dark:hover:bg-navy-700"
                          onClick={() => {
                            setRole(suggestedRole);
                            setFilteredRoles([]);
                          }}
                        >
                          {suggestedRole}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
                {/* Experience Dropdown */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Experience
                  </label>
                  <select
                    className="mt-1 w-full rounded-md border-[1px] border-gray-300 p-2 focus:border-blue-500 focus:ring-blue-500 dark:bg-navy-600 dark:text-white"
                    value={experience}
                    onChange={(e) => setExperience(e.target.value)}
                  >
                    <option value="">Select experience level</option>
                    <option value="0-1">0-1 years (Fresher)</option>
                    <option value="2-3">2-3 years (Intermediate)</option>
                    <option value="4-5">4-5 years (Experienced)</option>
                    <option value="5+">5+ years (Senior)</option>
                  </select>
                </div>
                {/* Location Input */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Location
                  </label>
                  <input
                    type="text"
                    className="mt-1 w-full rounded-md border-[1px] border-gray-300 p-2 focus:border-blue-500 focus:ring-blue-500 dark:bg-navy-600 dark:text-white"
                    placeholder="Enter the location"
                    value={location}
                    onChange={(e) => {
                      const input = e.target.value;
                      setLocation(input);
                      setFilteredLocations(
                        locations.filter((loc) =>
                          loc.toLowerCase().includes(input.toLowerCase())
                        )
                      );
                    }}
                  />
                  {location && (
                    <ul className="mt-2 max-h-32 overflow-auto rounded-md border border-gray-300 bg-white shadow-md dark:bg-navy-600">
                      {filteredLocations.map((suggestedLocation, index) => (
                        <li
                          key={index}
                          className="cursor-pointer p-2 hover:bg-gray-100 dark:hover:bg-navy-700"
                          onClick={() => {
                            setLocation(suggestedLocation);
                            setFilteredLocations([]);
                          }}
                        >
                          {suggestedLocation}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>

                {/* Skills Input */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Skills
                  </label>
                  <input
                    type="text"
                    className="mt-1 w-full rounded-md border-[1px] border-gray-300 p-2 focus:border-blue-500 focus:ring-blue-500 dark:bg-navy-600 dark:text-white"
                    placeholder="Type a skill"
                    onChange={(e) => {
                      const input = e.target.value;
                      setFilteredSkills(
                        skillsList.filter((skill) =>
                          skill.toLowerCase().includes(input.toLowerCase())
                        )
                      );
                    }}
                    onKeyDown={(e) => {
                      if (
                        e.key === "Enter" &&
                        e.target.value.trim() !== "" &&
                        !skills.includes(e.target.value.trim())
                      ) {
                        setSkills([...skills, e.target.value.trim()]);
                        e.target.value = "";
                      }
                    }}
                  />
                  {filteredSkills.length > 0 && (
                    <ul className="mt-2 max-h-32 overflow-auto rounded-md border border-gray-300 bg-white shadow-md dark:bg-navy-600">
                      {filteredSkills.map((suggestedSkill, index) => (
                        <li
                          key={index}
                          className="cursor-pointer p-2 hover:bg-gray-100 dark:hover:bg-navy-700"
                          onClick={() => {
                            if (!skills.includes(suggestedSkill)) {
                              setSkills([...skills, suggestedSkill]);
                            }
                            setFilteredSkills([]);
                          }}
                        >
                          {suggestedSkill}
                        </li>
                      ))}
                    </ul>
                  )}
                  <div className="mt-2 flex flex-wrap gap-2">
                    {skills.map((skill, index) => (
                      <span
                        key={index}
                        className="flex items-center rounded-full bg-indigo-100 px-3 py-1 text-xs text-indigo-800"
                      >
                        {skill}
                        <button
                          className="ml-2 text-indigo-600 hover:text-indigo-800"
                          onClick={() =>
                            setSkills(skills.filter((s) => s !== skill))
                          }
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                </div>

                <div className="mt-4 flex justify-between">
                  <button
                    type="button"
                    className="flex items-center rounded-md bg-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-400 dark:bg-gray-600 dark:text-white"
                    onClick={() => setStep(1)}
                  >
                    <HiChevronLeft className="mr-2" />
                    Back
                  </button>
                  <button
                    type="button"
                    className="flex items-center rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
                    onClick={handleSave}
                  >
                    Create Job Description
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobDescriptionModal;