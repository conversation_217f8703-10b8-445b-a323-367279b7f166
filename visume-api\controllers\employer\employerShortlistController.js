// Employer Shortlist and Unlock Operations

const pool = require("../../config/db");

// Fetch shortlisted profiles by employer ID
exports.getShortlistProfiles = async (req, res) => {
  const { emp_id } = req.params;

  if (!emp_id) {
    return res.status(400).json({ message: "Employer ID is required." });
  }

  try {
    const conn = await pool.promise().getConnection();

    try {
      const sqlQuery = `
        SELECT video_resume_id, status
        FROM employerprofiles
        WHERE emp_id = ?;
      `;

      const [employerProfiles] = await conn.query(sqlQuery, [emp_id]);

      if (employerProfiles.length === 0) {
        return res.status(404).json({
          message: "No shortlisted profiles found for this employer.",
        });
      }

      const statusMap = employerProfiles.reduce((acc, profile) => {
        acc[profile.video_resume_id] = profile.status;
        return acc;
      }, {});

      const videoProfileIdsArray = employerProfiles.map(
        (c) => c.video_resume_id
      );

      const candidateDataQuery = `
        SELECT vp.id AS profile_id, vp.video_profile_id, js.*, vp.* 
        FROM videoprofile vp
        INNER JOIN jobseeker js ON vp.cand_id = js.cand_id
        WHERE vp.id IN (?);
      `;

      const [candidateProfiles] = await conn.query(candidateDataQuery, [
        videoProfileIdsArray,
      ]);

      if (candidateProfiles.length === 0) {
        return res.status(404).json({
          message: "No candidate profiles found.",
        });
      }

      candidateProfiles.forEach((profile) => {
        profile.status = statusMap[profile.profile_id] || profile.status;
      });

      res.status(200).json({
        message: "Candidate profiles with jobseeker data fetched successfully.",
        data: candidateProfiles,
      });
    } finally {
      conn.release();
    }
  } catch (error) {
    console.error("Error fetching profiles:", error);
    return res.status(500).json({ message: "Failed to fetch profiles." });
  }
};

// Shortlist a video profile for an employer
exports.shortlistVideoProfile = async (req, res) => {
  const { emp_id, cand_id } = req.body;

  if (!emp_id || !cand_id) {
    return res
      .status(400)
      .json({ message: "Employer ID and Candidate ID are required." });
  }

  try {
    const conn = await pool.promise().getConnection();

    try {
      const checkCandidateQuery = "SELECT id, video_profile_id FROM videoprofile WHERE video_profile_id = ?";
      const [candidate] = await conn.query(checkCandidateQuery, [cand_id]);

      if (candidate.length === 0) {
        return res.status(404).json({ message: "Invalid Video Profile ID." });
      }
      const videoProfileId = candidate[0].id;

      const checkEmployerQuery = "SELECT id FROM employer WHERE id = ?";
      const [employer] = await conn.query(checkEmployerQuery, [emp_id]);

      if (employer.length === 0) {
        return res.status(404).json({ message: "Invalid Employer ID." });
      }

      const updateQuery = `
        INSERT INTO employerprofiles (emp_id, video_resume_id, status, shortlisted_at, unlocked_at)
        VALUES (?, ?, 'shortlisted', NOW(), NULL)
        ON DUPLICATE KEY UPDATE
          status = 'shortlisted',
          shortlisted_at = NOW(),
          unlocked_at = NULL;
      `;

      const [updateResult] = await conn.query(updateQuery, [emp_id, videoProfileId]);

      const selectQuery = `
        SELECT id, emp_id, video_resume_id, status, shortlisted_at, unlocked_at
        FROM employerprofiles
        WHERE video_resume_id = ? AND emp_id = ?;
      `;

      const [rows] = await conn.query(selectQuery, [videoProfileId, emp_id]);

      const { id, status, shortlisted_at, unlocked_at } = rows[0];
      return res.status(200).json({
        message: "Video profile shortlisted successfully.",
        data: {
          id,
          video_resume_id: videoProfileId,
          cand_id,
          status,
          shortlisted_at,
          unlocked_at,
        },
      });
    } finally {
      conn.release();
    }
  } catch (error) {
    console.error("Error shortlisting video profile:", error);
    return res
      .status(500)
      .json({ message: "Failed to shortlist video profile." });
  }
};

// UnShortlist a video profile for an employer
exports.unShortlistVideoProfile = async (req, res) => {
  const { emp_id, cand_id } = req.body;

  if (!emp_id || !cand_id) {
    return res
      .status(400)
      .json({ message: "Employer ID and Candidate ID are required." });
  }

  try {
    const conn = await pool.promise().getConnection();

    try {
      const checkEmployerQuery = "SELECT id FROM employer WHERE id = ?";
      const [employer] = await conn.query(checkEmployerQuery, [emp_id]);

      if (employer.length === 0) {
        return res.status(404).json({ message: "Invalid Employer ID." });
      }

      const checkVideoQuery =
        "SELECT id FROM videoprofile WHERE video_profile_id = ?";
      const [videoProfile] = await conn.query(checkVideoQuery, [cand_id]);

      if (videoProfile.length === 0) {
        return res.status(404).json({ message: "Invalid Video Resume ID." });
      }

      const { id: videoProfileId } = videoProfile[0];

      const checkShortlistQuery =
        "SELECT * FROM employerprofiles WHERE video_resume_id = ? AND emp_id = ?";
      const [shortlistedCandidate] = await conn.query(checkShortlistQuery, [
        videoProfileId,
        emp_id,
      ]);

      if (shortlistedCandidate.length === 0) {
        return res
          .status(404)
          .json({ message: "Candidate is not shortlisted by this employer." });
      }

      const deleteShortlistQuery =
        "DELETE FROM employerprofiles WHERE video_resume_id = ? AND emp_id = ?";
      const [deleteResult] = await conn.query(deleteShortlistQuery, [
        videoProfileId,
        emp_id,
      ]);

      if (deleteResult.affectedRows === 0) {
        return res
          .status(404)
          .json({ message: "Failed to unshortlist candidate." });
      }

      return res.status(200).json({
        message: "Video profile unshortlisted successfully.",
      });
    } finally {
      conn.release();
    }
  } catch (error) {
    console.error("Error unshortlisting video profile:", error);
    return res
      .status(500)
      .json({ message: "Failed to unshortlist video profile." });
  }
};

// Unlock a video profile for an employer
exports.unlockVideoProfile = async (req, res) => {
  const { emp_id, video_profile_id } = req.body;

  if (!emp_id || !video_profile_id) {
    return res
      .status(400)
      .json({ message: "Employer ID and Video Profile ID are required." });
  }

  try {
    const conn = await pool.promise().getConnection();

    try {
      const planIdQuery = `SELECT creditsLeft FROM employerplans WHERE emp_id = ?;`;
      const [planResult] = await conn.query(planIdQuery, [emp_id]);

      if (!planResult.length) {
        return res.status(404).json({ message: "Employer profile not found." });
      }

      const { creditsLeft } = planResult[0];

      if (creditsLeft <= 0) {
        return res.status(429).json({
          message:
            "Please recharge to unlock this video profile. Your credits are over.",
        });
      }

      const updateQuery = `
        UPDATE employerprofiles
        SET status = ?, unlocked_at = NOW()
        WHERE video_resume_id = ? AND emp_id = ? AND status = ?;
      `;

      const [updateResult] = await conn.query(updateQuery, [
        "unlocked",
        video_profile_id,
        emp_id,
        "shortlisted",
      ]);

      if (updateResult.affectedRows === 0) {
        return res.status(404).json({
          message: "Profile not found or not in the shortlisted status.",
        });
      }

      const deductCreditQuery = `
        UPDATE employerplans
        SET creditsLeft = creditsLeft - 1
        WHERE emp_id = ?;
      `;

      await conn.query(deductCreditQuery, [emp_id]);

      const selectUpdatedQuery = `
        SELECT *
        FROM employerprofiles
        WHERE video_resume_id = ? AND emp_id = ?;
      `;

      const [updatedRows] = await conn.query(selectUpdatedQuery, [
        video_profile_id,
        emp_id,
      ]);

      res.status(200).json({
        message: "Profile updated to unlocked successfully.",
        data: updatedRows[0],
      });
    } finally {
      conn.release();
    }
  } catch (error) {
    console.error("Error processing video profile update:", error);
    res.status(500).json({ message: "Failed to process profile update." });
  }
};

// Remove a video profile for an employer from shortlisted
exports.removeVideoProfile = async (req, res) => {
  const { emp_id, cand_id } = req.body;

  if (!emp_id || !cand_id) {
    return res
      .status(400)
      .json({ message: "Employer ID and Candidate ID are required." });
  }

  try {
    pool.getConnection((err, connection) => {
      if (err) {
        console.error("Error getting database connection:", err);
        return res
          .status(500)
          .json({ message: "Failed to connect to the database." });
      }

      const checkQuery = `
        SELECT status 
        FROM employerprofiles 
        WHERE cand_id = ? AND emp_id = ?;
      `;

      connection.query(checkQuery, [cand_id, emp_id], (err, results) => {
        if (err) {
          connection.release();
          console.error("Error checking video profile status:", err);
          return res
            .status(500)
            .json({ message: "Failed to check video profile status." });
        }

        if (results.length === 0) {
          connection.release();
          return res.status(404).json({ message: "Video profile not found." });
        }

        const profileStatus = results[0].status;

        if (profileStatus === "unlocked") {
          connection.release();
          return res
            .status(400)
            .json({ message: "Cannot delete an unlocked video profile." });
        }

        const deleteQuery = `
          DELETE FROM employerprofiles 
          WHERE cand_id = ? AND emp_id = ? AND status != 'unlocked';
        `;

        connection.query(deleteQuery, [cand_id, emp_id], (err, results) => {
          connection.release();

          if (err) {
            console.error("Error removing video profile from shortlist:", err);
            return res.status(500).json({
              message: "Failed to remove video profile from shortlist.",
            });
          }

          if (results.affectedRows === 0) {
            return res
              .status(404)
              .json({ message: "Video profile not found or already removed." });
          }

          res.status(200).json({
            message: "Video profile removed from shortlist successfully.",
          });
        });
      });
    });
  } catch (error) {
    console.error("Error removing video profile:", error);
    res
      .status(500)
      .json({ message: "Failed to remove video profile from shortlist." });
  }
};